{"id": "07482b44-87c3-47d0-8537-442127c4a8d3", "revision": 0, "last_node_id": 501, "last_link_id": 1057, "nodes": [{"id": 362, "type": "SetNode", "pos": [-917.0396728515625, 915.1357421875], "size": [210, 60], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "link": 960}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "Audio", "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}, "widget_ue_connectable": {}}, "widgets_values": ["Audio"], "color": "#233", "bgcolor": "#355"}, {"id": 391, "type": "easy cleanGpuUsed", "pos": [-80.97803497314453, 705.0254516601562], "size": [140, 26], "flags": {}, "order": 16, "mode": 4, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 717}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": []}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 375, "type": "GetNode", "pos": [24.52305030822754, 583.826904296875], "size": [210, 60], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": []}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["Reference_Image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 320, "type": "ModelSamplingSD3", "pos": [-1253.6351318359375, 549.1173095703125], "size": [368.7491149902344, 74.29029846191406], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "模型", "name": "model", "type": "MODEL", "link": 797}], "outputs": [{"label": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [811]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002], "color": "#222", "bgcolor": "#000"}, {"id": 390, "type": "easy cleanGpuUsed", "pos": [1452.5771484375, 602.0974731445312], "size": [140, 26], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 1051}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [799]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 317, "type": "TrimVideoLatent", "pos": [1522.103759765625, 778.5338745117188], "size": [331.843017578125, 65.58429718017578], "flags": {"collapsed": false}, "order": 52, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 799}, {"label": "trim_amount", "name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 567}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [800]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "TrimVideoLatent", "widget_ue_connectable": {"trim_amount": true}, "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}}, "widgets_values": [0], "color": "#233", "bgcolor": "#355"}, {"id": 372, "type": "ColorMatch", "pos": [1887.9442138671875, 20.069847106933594], "size": [211.53802490234375, 102], "flags": {}, "order": 57, "mode": 0, "inputs": [{"label": "参考图像", "name": "image_ref", "type": "IMAGE", "link": 682}, {"label": "目标图像", "name": "image_target", "type": "IMAGE", "link": 681}], "outputs": [{"label": "图像", "name": "image", "type": "IMAGE", "links": [860]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "ColorMatch", "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}, "widget_ue_connectable": {}}, "widgets_values": ["mkl", 0.4000000000000001], "color": "#233", "bgcolor": "#355"}, {"id": 423, "type": "easy cleanGpuUsed", "pos": [2190.771728515625, 225.2633514404297], "size": [210, 26], "flags": {}, "order": 58, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 860}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [846]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 425, "type": "easy clearCacheAll", "pos": [2185.7177734375, 325.0774230957031], "size": [210, 26], "flags": {}, "order": 59, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 846}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [848]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy clearCacheAll", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 344, "type": "VAEDecodeTiled", "pos": [1754.673095703125, 422.9673156738281], "size": [324.7507629394531, 150], "flags": {}, "order": 53, "mode": 0, "inputs": [{"label": "Latent", "name": "samples", "type": "LATENT", "link": 800}, {"label": "VAE", "name": "vae", "type": "VAE", "link": 623}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [695, 816, 982]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecodeTiled", "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}, "widget_ue_connectable": {}}, "widgets_values": [512, 64, 64, 8], "color": "#233", "bgcolor": "#355"}, {"id": 388, "type": "easy cleanGpuUsed", "pos": [9.451775550842285, 63.692195892333984], "size": [140, 26], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 712}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [713]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 392, "type": "easy cleanGpuUsed", "pos": [38.87966537475586, 195.3902130126953], "size": [140, 26], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 714}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [715]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 414, "type": "easy cleanGpuUsed", "pos": [689.5449829101562, 445.53961181640625], "size": [140, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 814}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [1050]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 371, "type": "LayerFilter: AddGrain", "pos": [1544.4560546875, 62.32513427734375], "size": [213.01808166503906, 106], "flags": {}, "order": 54, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 695}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [681]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerFilter: AddGrain", "widget_ue_connectable": {}}, "widgets_values": [0.05000000000000001, 0.5, 0.5000000000000001], "color": "rgba(34, 67, 111, 0.7)"}, {"id": 373, "type": "GetNode", "pos": [1882.620849609375, 257.1000671386719], "size": [210, 50], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [682]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["Reference_Image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 374, "type": "SetNode", "pos": [-1157.9234619140625, 887.58837890625], "size": [210, 60], "flags": {"collapsed": true}, "order": 20, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 908}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "Reference_Image", "widget_ue_connectable": {}}, "widgets_values": ["Reference_Image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 353, "type": "GetNode", "pos": [500.2390441894531, 840.5663452148438], "size": [210, 60], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [622, 623]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 477, "type": "GetNode", "pos": [495.6410217285156, 1196.76171875], "size": [210, 34], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [975]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 315, "type": "<PERSON><PERSON>", "pos": [-175.43719482421875, 829.7069091796875], "size": [292.97216796875, 87.4052505493164], "flags": {}, "order": 4, "mode": 4, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": null}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [717]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "<PERSON><PERSON>", "widget_ue_connectable": {}, "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}}, "widgets_values": [0.17000000000000004, 0.28]}, {"id": 389, "type": "easy cleanGpuUsed", "pos": [1087.2105712890625, 1149.086181640625], "size": [140, 26], "flags": {}, "order": 48, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 992}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [1049]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 476, "type": "ConditioningConcat", "pos": [917.7021484375, 1167.2928466796875], "size": [228.38671875, 46], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "conditioning_to", "name": "conditioning_to", "type": "CONDITIONING", "link": 972}, {"label": "conditioning_from", "name": "conditioning_from", "type": "CONDITIONING", "link": 973}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [992]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ConditioningConcat", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 447, "type": "PreviewImage", "pos": [224.00502014160156, 1403.18310546875], "size": [210, 258], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1006}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 435, "type": "LayerUtility: PurgeVRAM", "pos": [3141.708740234375, 545.4080810546875], "size": [210, 82], "flags": {"collapsed": false}, "order": 64, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 862}], "outputs": [], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 479, "type": "VHS_VideoCombine", "pos": [2444.088134765625, 837.0182495117188], "size": [615.0880737304688, 334], "flags": {}, "order": 56, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 982}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": 983}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "a7ce59e381934733bfae03b1be029756d6ce936d", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00008-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 24, "workflow": "AnimateDiff_00008.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_72c291bf\\AnimateDiff_00008-audio.mp4"}}}, "color": "#233", "bgcolor": "#355"}, {"id": 382, "type": "PreviewImage", "pos": [-216.0465087890625, 1786.0001220703125], "size": [210, 258], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1023}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 377, "type": "SetNode", "pos": [-500.7794189453125, 678.28515625], "size": [210, 50], "flags": {"collapsed": true}, "order": 46, "mode": 0, "inputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "link": 812}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "vace_model", "widget_ue_connectable": {}}, "widgets_values": ["vace_model"], "color": "#223", "bgcolor": "#335"}, {"id": 399, "type": "CLIPLoader", "pos": [-1207.2584228515625, -427.9713439941406], "size": [390, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "cpu"]}, {"id": 437, "type": "CLIPLoaderGGUF", "pos": [-1110.6424560546875, -102.69561004638672], "size": [359.7388916015625, 86.94934844970703], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [868]}], "properties": {"cnr_id": "ComfyUI-GGUF", "ver": "a2b75978fd50c0227a58316619b79d525b88e570", "Node name for S&R": "CLIPLoaderGGUF", "widget_ue_connectable": {}}, "widgets_values": ["umt5-xxl-encoder-Q4_K_S.gguf", "wan"], "color": "#432", "bgcolor": "#653"}, {"id": 355, "type": "ModelPatchTorchSettings", "pos": [-1649.9344482421875, 455.8157958984375], "size": [334.88568115234375, 63.247589111328125], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 632}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [797]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "ModelPatchTorchSettings", "widget_ue_connectable": {}}, "widgets_values": [true], "color": "#222", "bgcolor": "#000"}, {"id": 354, "type": "PathchSageAttentionKJ", "pos": [-1671.7091064453125, 317.6650695800781], "size": [336.140869140625, 62.704200744628906], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 796}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [632]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#222", "bgcolor": "#000"}, {"id": 393, "type": "LayerUtility: PurgeVRAM", "pos": [1951.0518798828125, 775.8056030273438], "size": [210, 82], "flags": {"collapsed": false}, "order": 55, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 816}], "outputs": [], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 398, "type": "WanVideoTeaCacheKJ", "pos": [-1601.51904296875, 90.70098114013672], "size": [340.20001220703125, 154], "flags": {}, "order": 17, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 887}], "outputs": [{"label": "model", "name": "model", "type": "MODEL", "slot_index": 0, "links": [888]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "b560966027157a484c60060f05f3455e480bfcbc", "Node name for S&R": "WanVideoTeaCacheKJ", "widget_ue_connectable": {}}, "widgets_values": [0.20000000000000004, 0.10000000000000002, 1, "main_device", "14B"]}, {"id": 397, "type": "<PERSON> Lora <PERSON> (rgthree)", "pos": [-1186.9735107421875, 125.59481811523438], "size": [467.2092590332031, 262], "flags": {}, "order": 24, "mode": 0, "inputs": [{"dir": 3, "label": "模型", "name": "model", "type": "MODEL", "link": 888}, {"dir": 3, "label": "CLIP", "name": "clip", "type": "CLIP", "link": 868}], "outputs": [{"dir": 4, "label": "模型", "name": "MODEL", "shape": 3, "type": "MODEL", "links": [796]}, {"dir": 4, "label": "CLIP", "name": "CLIP", "shape": 3, "type": "CLIP", "links": [745, 746]}], "properties": {"cnr_id": "rgthree-comfy", "ver": "84a146fee39f7b3a8c6631dcac29bc13b077eb49", "Show Strengths": "Single Strength", "widget_ue_connectable": {}}, "widgets_values": [{}, {"type": "PowerLoraLoaderHeaderWidget"}, {"on": true, "lora": "Wan2.1_FusionX\\Wan2.1_T2V_14B_FusionX_LoRA.safetensors", "strength": 0.7, "strengthTwo": null}, {"on": false, "lora": "Wan2.1_FusionX\\Wan2.1_I2V_14B_FusionX_LoRA.safetensors", "strength": 1, "strengthTwo": null}, {"on": false, "lora": "Wan2.1_FusionX\\Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", "strength": 1, "strengthTwo": null}, {"on": false, "lora": "Wan2.1_FusionX\\WAN 视频的细节增强器.safetensors", "strength": 1, "strengthTwo": null}, {"on": false, "lora": "Wan2.1_FusionX\\[Wan2.1]定向摄影.safetensors", "strength": 1, "strengthTwo": null}, {"on": false, "lora": "Wan2.1_FusionX\\Wan21_CausVid_14B_T2V_lora_rank32_v2.safetensors", "strength": 0.5, "strengthTwo": null}, {}, ""]}, {"id": 335, "type": "UnetLoaderGGUF", "pos": [-1688.14794921875, -110.064453125], "size": [406.7340393066406, 99.53983306884766], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [887]}], "properties": {"cnr_id": "comfyui-gguf", "ver": "6570efec6992015085f11b84e42d32f6cc71e8b7", "Node name for S&R": "UnetLoaderGGUF", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.1_T2V_14B_LightX2V_StepCfgDistill_VACE-Q4_K_S.gguf"], "color": "#222", "bgcolor": "#000"}, {"id": 243, "type": "VHS_VideoCombine", "pos": [3781.8603515625, 829.4694213867188], "size": [632.0671997070312, 1445.499755859375], "flags": {}, "order": 63, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 861}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": 961}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a3b1eb433a95d33e71bca634b8307a238d7e425", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 48, "loop_count": 0, "filename_prefix": "video-clips/wan2/v2v_int_upscaled", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "v2v_int_upscaled_00298-audio.mp4", "subfolder": "video-clips\\wan2", "type": "output", "format": "video/h264-mp4", "frame_rate": 48, "workflow": "v2v_int_upscaled_00298.png", "fullpath": "G:\\ComfyUI\\ComfyUI\\output\\video-clips\\wan2\\v2v_int_upscaled_00298-audio.mp4"}}}, "color": "#323", "bgcolor": "#535"}, {"id": 427, "type": "RIFE VFI", "pos": [2665.12353515625, 357.3209533691406], "size": [313.5589904785156, 198], "flags": {"collapsed": false}, "order": 60, "mode": 4, "inputs": [{"label": "图像", "name": "frames", "type": "IMAGE", "link": 848}, {"label": "插值规则(可选)", "name": "optional_interpolation_states", "shape": 7, "type": "INTERPOLATION_STATES", "link": null}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [863]}], "properties": {"cnr_id": "comfyui-frame-interpolation", "ver": "1.0.7", "Node name for S&R": "RIFE VFI", "widget_ue_connectable": {}}, "widgets_values": ["rife47.pth", 10, 1, true, true, 1], "color": "#323", "bgcolor": "#535"}, {"id": 436, "type": "CR Upscale Image", "pos": [2659.48779296875, 32.7497673034668], "size": [315, 222], "flags": {}, "order": 61, "mode": 4, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 863}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [866]}, {"label": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Upscale Image", "widget_ue_connectable": {}}, "widgets_values": ["4xNMKDSuperscale_4xNMKDSuperscale.pt", "rescale", 2, 720, "lanc<PERSON>s", "true", 8]}, {"id": 422, "type": "RIFE VFI", "pos": [3209.369384765625, 960.198974609375], "size": [478.8000183105469, 198], "flags": {}, "order": 62, "mode": 0, "inputs": [{"label": "图像", "name": "frames", "type": "IMAGE", "link": 866}, {"label": "插值规则(可选)", "name": "optional_interpolation_states", "shape": 7, "type": "INTERPOLATION_STATES", "link": null}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [861, 862]}], "properties": {"cnr_id": "comfyui-frame-interpolation", "ver": "1.0.7", "Node name for S&R": "RIFE VFI", "widget_ue_connectable": {"multiplier": true}}, "widgets_values": ["rife47.pth", 10, 2, true, true, 1]}, {"id": 474, "type": "ConditioningConcat", "pos": [902.6494750976562, 1014.2572021484375], "size": [228.38671875, 46], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "conditioning_to", "name": "conditioning_to", "type": "CONDITIONING", "link": 964}, {"label": "conditioning_from", "name": "conditioning_from", "type": "CONDITIONING", "link": 966}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [1048]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ConditioningConcat", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 39, "type": "VAELoader", "pos": [-1920.33740234375, 600.09228515625], "size": [336.1007385253906, 71.43658447265625], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [466]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.18", "Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 250, "type": "SetNode", "pos": [-1469.322265625, 653.418212890625], "size": [210, 58], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "link": 466}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "VAE", "widget_ue_connectable": {}}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 495, "type": "GetImageSizeAndCount", "pos": [-215.4146728515625, -461.26800537109375], "size": [190.86483764648438, 86], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": null}], "outputs": [{"label": "图像", "name": "image", "type": "IMAGE", "links": null}, {"label": "宽度", "name": "width", "type": "INT", "links": null}, {"label": "高度", "name": "height", "type": "INT", "links": null}, {"label": "数量", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "f7eb33abc80a2aded1b46dff0dd14d07856a7d50", "Node name for S&R": "GetImageSizeAndCount", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 420, "type": "DownloadAndLoadDepthAnythingV2Model", "pos": [-1271.2945556640625, 1864.909423828125], "size": [372.3266296386719, 61.27016067504883], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "da_v2_model", "name": "da_v2_model", "type": "DAMODEL", "links": [839]}], "properties": {"cnr_id": "comfyui-depthanythingv2", "ver": "003d7b44bafd3a8a4c3693a9ca3ddcd72f4883ab", "Node name for S&R": "DownloadAndLoadDepthAnythingV2Model", "widget_ue_connectable": {}}, "widgets_values": ["depth_anything_v2_vitl_fp32.safetensors"]}, {"id": 379, "type": "ImageResizeKJv2", "pos": [-1107.144775390625, 2074.267333984375], "size": [270, 266], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": null}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": []}, {"label": "width", "name": "width", "type": "INT", "links": []}, {"label": "height", "name": "height", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "ImageResizeKJv2", "widget_ue_connectable": {}}, "widgets_values": [480, 832, "lanc<PERSON>s", "pad", "255,255,255", "center", 16, "cpu"], "color": "#232", "bgcolor": "#353"}, {"id": 421, "type": "DepthAnything_V2", "pos": [-882.3172607421875, 2069.167724609375], "size": [214.20001220703125, 46], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "da_model", "name": "da_model", "type": "DAMODEL", "link": 839}, {"label": "images", "name": "images", "type": "IMAGE", "link": null}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": []}], "properties": {"cnr_id": "comfyui-depthanythingv2", "ver": "003d7b44bafd3a8a4c3693a9ca3ddcd72f4883ab", "Node name for S&R": "DepthAnything_V2", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 351, "type": "LoadAndResizeImage", "pos": [-1713.73779296875, 831.5967407226562], "size": [524.2059326171875, 815.374755859375], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"label": "图像", "name": "image", "type": "IMAGE", "links": [908, 1032]}, {"label": "遮罩", "name": "mask", "type": "MASK", "links": null}, {"label": "宽度", "name": "width", "type": "INT", "links": []}, {"label": "高度", "name": "height", "type": "INT", "links": []}, {"label": "image_path", "name": "image_path", "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "d57154c3a808b8a3f232ed293eaa2d000867c884", "Node name for S&R": "LoadAndResizeImage", "aux_id": "kijai/ComfyUI-KJNodes", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_temp_mgfgy_00001_.png", true, 720, 10000, 1, true, 16, "alpha", "", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 496, "type": "ImageResizeKJ", "pos": [-692.3478393554688, 865.939453125], "size": [270, 218], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 1027}, {"label": "参考图像", "name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [1029]}, {"label": "宽度", "name": "width", "type": "INT", "links": [1033, 1038]}, {"label": "高度", "name": "height", "type": "INT", "links": [1034, 1040]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "f7eb33abc80a2aded1b46dff0dd14d07856a7d50", "Node name for S&R": "ImageResizeKJ", "widget_ue_connectable": {}}, "widgets_values": [480, 832, "lanc<PERSON>s", true, 16, "disabled"]}, {"id": 497, "type": "ImageResizeKJ", "pos": [-670.622802734375, 1168.5203857421875], "size": [270, 218], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 1032}, {"label": "参考图像", "name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 1033}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1034}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [1043]}, {"label": "宽度", "name": "width", "type": "INT", "links": []}, {"label": "高度", "name": "height", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "f7eb33abc80a2aded1b46dff0dd14d07856a7d50", "Node name for S&R": "ImageResizeKJ", "widget_ue_connectable": {}}, "widgets_values": [480, 832, "lanc<PERSON>s", false, 16, "center"]}, {"id": 473, "type": "VHS_LoadVideo", "pos": [-1028.5260009765625, 828.4669189453125], "size": [274.02325439453125, 775.1532592773438], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [1027]}, {"label": "帧计数", "name": "frame_count", "type": "INT", "links": [1041]}, {"label": "音频", "name": "audio", "type": "AUDIO", "links": [960, 961, 983]}, {"label": "视频信息", "name": "video_info", "type": "VHS_VIDEOINFO", "links": []}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "a7ce59e381934733bfae03b1be029756d6ce936d", "Node name for S&R": "VHS_LoadVideo", "widget_ue_connectable": {}}, "widgets_values": {"video": "#dance #tiktokdance #chinesegirl #fyp #viral #trending #dancer #dance... (7477145882689916193).mp4", "force_rate": 24, "custom_width": 0, "custom_height": 0, "frame_load_cap": 70, "skip_first_frames": 40, "select_every_nth": 1, "format": "AnimateDiff", "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"filename": "#dance #tiktokdance #chinesegirl #fyp #viral #trending #dancer #dance... (7477145882689916193).mp4", "type": "input", "format": "video/mp4", "force_rate": 24, "custom_width": 0, "custom_height": 0, "frame_load_cap": 70, "skip_first_frames": 40, "select_every_nth": 1}}}, "color": "#232", "bgcolor": "#353"}, {"id": 412, "type": "WanVideoNAG", "pos": [-896.6033325195312, 525.4724731445312], "size": [270, 126], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 811}, {"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 1016}], "outputs": [{"label": "model", "name": "model", "type": "MODEL", "links": [812, 1044]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "b560966027157a484c60060f05f3455e480bfcbc", "Node name for S&R": "WanVideoNAG", "widget_ue_connectable": {}}, "widgets_values": [11, 0.25, 2.5]}, {"id": 325, "type": "CLIPTextEncode", "pos": [4.812968730926514, 393.4250183105469], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "CLIP", "name": "clip", "type": "CLIP", "link": 746}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [714, 968, 1016]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["残影，模糊，色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走 , extra hands, extra arms, extra legs", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 500, "type": "WanVideoKsampler", "pos": [1094.7198486328125, 397.44940185546875], "size": [270, 474.0000305175781], "flags": {}, "order": 50, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 1047}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 1048}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 1049}, {"label": "video_latents", "name": "video_latents", "type": "LATENT", "link": 1050}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [1051]}], "properties": {"cnr_id": "ComfyUI-WanVideoKsampler", "ver": "e92a703670b6d2ed0748f3c25ae551e7e05d0ea8", "Node name for S&R": "WanVideoKsampler", "widget_ue_connectable": {}}, "widgets_values": [727287275421093, "randomize", 4, 1, "uni_pc", "simple", 1]}, {"id": 494, "type": "WanVideoEnhanceAVideoKJ", "pos": [648.765380859375, -106.96651458740234], "size": [326.1216735839844, 78], "flags": {}, "order": 49, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 1045}, {"label": "latent", "name": "latent", "type": "LATENT", "link": 1046}], "outputs": [{"label": "model", "name": "model", "type": "MODEL", "links": [1047]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "f7eb33abc80a2aded1b46dff0dd14d07856a7d50", "Node name for S&R": "WanVideoEnhanceAVideoKJ", "widget_ue_connectable": {}}, "widgets_values": [2]}, {"id": 493, "type": "CFGZeroStarAndInit", "pos": [-429.4583435058594, 424.8532409667969], "size": [270, 82], "flags": {}, "order": 47, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 1044}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [1045]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "f7eb33abc80a2aded1b46dff0dd14d07856a7d50", "Node name for S&R": "CFGZeroStarAndInit", "widget_ue_connectable": {}}, "widgets_values": [true, 0]}, {"id": 343, "type": "K<PERSON><PERSON><PERSON>", "pos": [1466.84912109375, 1137.0596923828125], "size": [346.5467224121094, 474.0000305175781], "flags": {}, "order": 23, "mode": 4, "inputs": [{"label": "模型", "name": "model", "type": "MODEL", "link": 691}, {"label": "正面条件", "name": "positive", "type": "CONDITIONING", "link": null}, {"label": "负面条件", "name": "negative", "type": "CONDITIONING", "link": null}, {"label": "Latent", "name": "latent_image", "type": "LATENT", "link": null}], "outputs": [{"label": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {}, "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}}, "widgets_values": [966202033440610, "randomize", 4, 1, "uni_pc", "simple", 1], "color": "#233", "bgcolor": "#355"}, {"id": 378, "type": "GetNode", "pos": [1357.2940673828125, 1337.37890625], "size": [210, 60], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [691]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["vace_model"], "color": "#223", "bgcolor": "#335"}, {"id": 475, "type": "WanVaceToVideo", "pos": [805.0972900390625, 1354.245849609375], "size": [313.4136962890625, 426.0850524902344], "flags": {}, "order": 36, "mode": 4, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 967}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 968}, {"label": "vae", "name": "vae", "type": "VAE", "link": 975}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": null}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK", "link": null}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": null}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [966]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [973]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": []}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "WanVaceToVideo", "widget_ue_connectable": {}, "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}}, "widgets_values": [480, 832, 81, 1, 1], "color": "#233", "bgcolor": "#355"}, {"id": 501, "type": "CLIPTextEncode", "pos": [-323.9176940917969, -265.44732666015625], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "CLIP", "name": "clip", "type": "CLIP", "link": null}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["一个女孩，跳舞，穿着一件( (贴身:1.5) (白色连衣裙) ), 身材苗条，身材瘦，连衣裙只有一种颜色，小腹收缩，表情自然，(杰作, 最高质量), 背景是白色窗帘\n[a woman in a skirt:a woman wearing a ((form-fitting white sheath dress)), (smooth silhouette):0.4]", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 316, "type": "PreviewImage", "pos": [204.33995056152344, 1042.5645751953125], "size": [210, 258], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 923}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}, "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}}, "widgets_values": []}, {"id": 329, "type": "WanVaceToVideo", "pos": [484.0909729003906, 569.6888427734375], "size": [313.4136962890625, 426.0850524902344], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 713}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 715}, {"label": "vae", "name": "vae", "type": "VAE", "link": 622}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 1057}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK", "link": null}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 1043}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 1038}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1040}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 1041}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [964]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [972]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": [814, 1046]}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": [567]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "WanVaceToVideo", "widget_ue_connectable": {}, "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}}, "widgets_values": [480, 832, 81, 1, 1], "color": "#233", "bgcolor": "#355"}, {"id": 485, "type": "Image Rembg (Remove Background)", "pos": [-686.8126831054688, 1561.9443359375], "size": [364.8125, 250], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1029}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [1023, 1024, 1052]}], "properties": {"cnr_id": "was-ns", "ver": "3.0.0", "Node name for S&R": "Image Rembg (Remove Background)", "widget_ue_connectable": {}}, "widgets_values": [true, "u2net", false, false, false, 240, 10, 10, "none"]}, {"id": 330, "type": "CLIPTextEncode", "pos": [-451.1152648925781, 133.70826721191406], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "CLIP", "name": "clip", "type": "CLIP", "link": 745}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [712, 967]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["一个女孩，跳舞，穿着一件( (贴身:1.5) (白色连衣裙) ), S型身材苗条，身材瘦，连衣裙只有一种颜色，服装保持一致，身材保持一致，表情自然，(杰作, 最高质量), 背景是白色窗帘\n[a woman in a skirt:a woman wearing a ((form-fitting white sheath dress)), (smooth silhouette):0.4]", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 364, "type": "DWPreprocessor", "pos": [-223.33871459960938, 1044.504638671875], "size": [294.72265625, 222], "flags": {}, "order": 32, "mode": 4, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 1052}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [923, 1001]}, {"label": "姿态关键点", "name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT", "links": null}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "83463c2e4b04e729268e57f638b4212e0da4badc", "Node name for S&R": "DWPreprocessor", "ttNbgOverride": {"color": "#233", "bgcolor": "#355", "groupcolor": "#8AA"}, "widget_ue_connectable": {"resolution": true}}, "widgets_values": ["enable", "enable", "disable", 512, "yolox_l.onnx", "dw-ll_ucoco_384_bs5.torchscript.pt", "disable"]}, {"id": 395, "type": "ImageBlend", "pos": [-140.98245239257812, 1601.6258544921875], "size": [270, 102], "flags": {}, "order": 39, "mode": 4, "inputs": [{"label": "图像1", "name": "image1", "type": "IMAGE", "link": 1000}, {"label": "图像2", "name": "image2", "type": "IMAGE", "link": 1001}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "ImageBlend", "widget_ue_connectable": {}}, "widgets_values": [0.20000000000000004, "normal"]}, {"id": 460, "type": "DepthAnythingV2Preprocessor", "pos": [-271.2648010253906, 1365.85888671875], "size": [292.8648376464844, 82], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 1024}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [1000, 1006, 1057]}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "59b027e088c1c8facf7258f6e392d16d204b4d27", "Node name for S&R": "DepthAnythingV2Preprocessor", "widget_ue_connectable": {}}, "widgets_values": ["depth_anything_v2_vitb.pth", 768]}], "links": [[466, 39, 0, 250, 0, "*"], [567, 329, 3, 317, 1, "INT"], [622, 353, 0, 329, 2, "VAE"], [623, 353, 0, 344, 1, "VAE"], [632, 354, 0, 355, 0, "MODEL"], [681, 371, 0, 372, 1, "IMAGE"], [682, 373, 0, 372, 0, "IMAGE"], [691, 378, 0, 343, 0, "MODEL"], [695, 344, 0, 371, 0, "IMAGE"], [712, 330, 0, 388, 0, "*"], [713, 388, 0, 329, 0, "CONDITIONING"], [714, 325, 0, 392, 0, "*"], [715, 392, 0, 329, 1, "CONDITIONING"], [717, 315, 0, 391, 0, "*"], [745, 397, 1, 330, 0, "CLIP"], [746, 397, 1, 325, 0, "CLIP"], [796, 397, 0, 354, 0, "MODEL"], [797, 355, 0, 320, 0, "MODEL"], [799, 390, 0, 317, 0, "LATENT"], [800, 317, 0, 344, 0, "LATENT"], [811, 320, 0, 412, 0, "MODEL"], [812, 412, 0, 377, 0, "MODEL"], [814, 329, 2, 414, 0, "*"], [816, 344, 0, 393, 0, "*"], [839, 420, 0, 421, 0, "DAMODEL"], [846, 423, 0, 425, 0, "*"], [848, 425, 0, 427, 0, "IMAGE"], [860, 372, 0, 423, 0, "*"], [861, 422, 0, 243, 0, "IMAGE"], [862, 422, 0, 435, 0, "*"], [863, 427, 0, 436, 0, "IMAGE"], [866, 436, 0, 422, 0, "IMAGE"], [868, 437, 0, 397, 1, "CLIP"], [887, 335, 0, 398, 0, "MODEL"], [888, 398, 0, 397, 0, "MODEL"], [908, 351, 0, 374, 0, "IMAGE"], [923, 364, 0, 316, 0, "IMAGE"], [960, 473, 2, 362, 0, "AUDIO"], [961, 473, 2, 243, 1, "AUDIO"], [964, 329, 0, 474, 0, "CONDITIONING"], [966, 475, 0, 474, 1, "CONDITIONING"], [967, 330, 0, 475, 0, "CONDITIONING"], [968, 325, 0, 475, 1, "CONDITIONING"], [972, 329, 1, 476, 0, "CONDITIONING"], [973, 475, 1, 476, 1, "CONDITIONING"], [975, 477, 0, 475, 2, "VAE"], [982, 344, 0, 479, 0, "IMAGE"], [983, 473, 2, 479, 1, "AUDIO"], [992, 476, 0, 389, 0, "*"], [1000, 460, 0, 395, 0, "IMAGE"], [1001, 364, 0, 395, 1, "IMAGE"], [1006, 460, 0, 447, 0, "IMAGE"], [1016, 325, 0, 412, 1, "CONDITIONING"], [1023, 485, 0, 382, 0, "IMAGE"], [1024, 485, 0, 460, 0, "IMAGE"], [1027, 473, 0, 496, 0, "IMAGE"], [1029, 496, 0, 485, 0, "IMAGE"], [1032, 351, 0, 497, 0, "IMAGE"], [1033, 496, 1, 497, 2, "INT"], [1034, 496, 2, 497, 3, "INT"], [1038, 496, 1, 329, 6, "INT"], [1040, 496, 2, 329, 7, "INT"], [1041, 473, 1, 329, 8, "INT"], [1043, 497, 0, 329, 5, "IMAGE"], [1044, 412, 0, 493, 0, "MODEL"], [1045, 493, 0, 494, 0, "MODEL"], [1046, 329, 2, 494, 1, "LATENT"], [1047, 494, 0, 500, 0, "MODEL"], [1048, 474, 0, 500, 1, "CONDITIONING"], [1049, 389, 0, 500, 2, "CONDITIONING"], [1050, 414, 0, 500, 3, "LATENT"], [1051, 500, 0, 390, 0, "*"], [1052, 485, 0, 364, 0, "IMAGE"], [1057, 460, 0, 329, 3, "IMAGE"]], "groups": [{"id": 2, "title": "Wan Vace Pass", "bounding": [-321.49884033203125, 40.59038162231445, 1713.1256103515625, 1633.1259765625], "color": "#444", "font_size": 24, "flags": {"pinned": true}}, {"id": 9, "title": "Video Interpolation", "bounding": [1429.0943603515625, 42.73270797729492, 1065.6627197265625, 1628.1727294921875], "color": "#444", "font_size": 24, "flags": {"pinned": true}}, {"id": 10, "title": "Wan Models", "bounding": [-1592.5482177734375, 50.99055480957031, 1201.91748046875, 686.9229125976562], "color": "#444", "font_size": 24, "flags": {"pinned": true}}, {"id": 16, "title": "Load control video", "bounding": [-968.589599609375, 768.3142700195312, 583.7946166992188, 895.41064453125], "color": "#8A8", "font_size": 24, "flags": {"pinned": true}}, {"id": 17, "title": "Load reference image", "bounding": [-1592.5042724609375, 767.5048217773438, 529.327392578125, 893.0370483398438], "color": "#8A8", "font_size": 24, "flags": {"pinned": true}}, {"id": 18, "title": "Image preprocessing", "bounding": [-287.99639892578125, 767.0662841796875, 675.2719116210938, 879.56396484375], "color": "#444", "font_size": 24, "flags": {}}, {"id": 19, "title": "放大", "bounding": [2630.3857421875, -44.43189239501953, 374.697265625, 624.0225830078125], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.45, "offset": [2960.387937757704, -33.52555338541674]}, "frontendVersion": "1.23.4", "node_versions": {"comfy-core": "0.3.18", "ComfyUI-GGUF": "5875c52f59baca3a9372d68c43a3775e21846fe0"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "ue_links": [], "links_added_by_ue": []}, "version": 0.4}